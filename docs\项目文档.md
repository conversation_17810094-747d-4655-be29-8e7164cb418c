# EduFusionCenter 项目文档

## 项目概述

EduFusionCenter（产教融合中心）是一个综合性的楼宇管理和视频监控系统，集成了楼宇管理、设备监控、摄像头管理、视频流处理、用户管理等多个功能模块。

## 最新更新

### 2024-03-15 - API接口文档完善
- ✅ **新增**：完整的API接口文档
- ✅ **新增**：视频流媒体API文档（Node.js服务器）
- ✅ **新增**：Java Web API文档
- ✅ **新增**：数据模型定义
- ✅ **新增**：使用示例和错误处理说明

#### 主要API接口
1. **视频流媒体API** (端口3001)
   - RTSP连接测试
   - 视频流启动/停止
   - 流状态监控
   - 缓存管理
   - 健康检查

2. **Java Web API** (端口8080)
   - 用户认证管理
   - 摄像头控制
   - 房间活动管理
   - 楼层布局API
   - 生产线管理
   - 系统管理

#### 技术特性
- **双技术栈架构**：Java Spring MVC + Node.js
- **实时视频流**：RTSP转HLS/FLV格式
- **RESTful设计**：统一的API接口规范
- **错误恢复**：自动重连和故障处理
- **多格式支持**：HLS和FLV视频格式

## 项目结构

```
EduFusionCenter/
├── src/main/java/com/building/     # Java源代码
│   ├── controller/                 # Spring MVC控制器
│   ├── service/                   # 业务逻辑层
│   ├── dao/                       # 数据访问层
│   ├── model/                     # 数据模型
│   ├── servlet/                   # Servlet类
│   └── util/                      # 工具类
├── src/main/webapp/               # Web资源
├── stream-server.js               # Node.js视频流服务器
├── package.json                   # Node.js依赖配置
├── pom.xml                       # Maven项目配置
└── docs_organized/               # 项目文档
    ├── api/rest-api/             # API接口文档
    ├── development/              # 开发指南
    ├── guides/                   # 使用手册
    └── system/                   # 系统设计文档
```

## 核心功能模块

### 1. 视频流媒体服务 (Node.js)
- **RTSP转码**：将RTSP视频流转换为Web可播放格式
- **多格式支持**：HLS（HTTP Live Streaming）和FLV格式
- **实时监控**：流状态监控和管理
- **自动恢复**：连接断开自动重连机制
- **缓存管理**：HLS分段文件缓存清理

### 2. 楼宇管理系统 (Java)
- **房间管理**：房间信息维护和查询
- **楼层布局**：可视化楼层平面图
- **活动记录**：房间使用活动跟踪
- **统计分析**：房间使用率统计

### 3. 设备监控系统
- **设备管理**：设备信息维护和状态监控
- **参数配置**：设备参数设置和调整
- **维护记录**：设备维护历史记录
- **告警管理**：设备异常告警处理

### 4. 摄像头管理系统
- **摄像头控制**：连接、断开、PTZ控制
- **视频流集成**：与Node.js流媒体服务器集成
- **批量操作**：支持批量摄像头操作
- **状态监控**：实时摄像头状态监控

### 5. 用户管理系统
- **用户认证**：基于Session的用户登录认证
- **权限管理**：角色基础的权限控制
- **安全设置**：密码策略和登录限制
- **会话管理**：用户会话状态管理

### 6. 生产线管理
- **产线监控**：生产线状态实时监控
- **数据统计**：生产数据统计分析
- **设备集成**：与设备监控系统集成
- **报表生成**：生产报表自动生成

## 技术架构

### 后端技术栈
- **Java**：Spring MVC框架
- **Node.js**：Express.js + FFmpeg视频处理
- **数据库**：MySQL数据存储
- **Web服务器**：Apache Tomcat

### 前端技术栈
- **HTML5/CSS3**：响应式页面设计
- **JavaScript**：前端交互逻辑
- **Bootstrap**：UI组件库
- **Chart.js**：数据可视化图表
- **Video.js**：视频播放器

### 视频处理技术
- **FFmpeg**：视频转码和处理
- **HLS协议**：HTTP Live Streaming
- **FLV格式**：Flash Video格式
- **WebSocket**：实时通信（预留）

## 部署环境

### 系统要求
- **操作系统**：Windows/Linux
- **Java**：JDK 8+
- **Node.js**：14.0+
- **FFmpeg**：4.0+
- **数据库**：MySQL 5.7+

### 端口配置
- **Java Web服务器**：8080
- **Node.js流媒体服务器**：3001
- **数据库服务器**：3306

### 启动步骤
1. 启动MySQL数据库服务
2. 部署Java Web应用到Tomcat
3. 启动Node.js流媒体服务器
4. 访问系统主页进行配置

## 开发指南

### 开发环境搭建
1. 安装JDK 8+和Maven
2. 安装Node.js和npm
3. 安装FFmpeg并配置环境变量
4. 配置MySQL数据库
5. 导入项目到IDE

### 代码规范
- **Java代码**：遵循阿里巴巴Java开发手册
- **JavaScript代码**：使用ES6+语法
- **数据库设计**：遵循第三范式
- **API设计**：遵循RESTful规范

### 测试指南
- **单元测试**：使用JUnit进行Java代码测试
- **集成测试**：测试API接口功能
- **性能测试**：视频流处理性能测试
- **兼容性测试**：多浏览器兼容性测试

## 文档索引

### API文档
- [完整API接口文档](../docs_organized/api/rest-api/API接口文档.md)
- [RESTful API设计详解](./RESTful-API-设计详解.md)

### 开发文档
- [开发指南](../docs_organized/development/01-开发指南.md)
- [项目结构分析与部署指南](../docs_organized/development/03-项目结构分析与部署指南.md)

### 系统文档
- [系统功能设计](../docs_organized/system/01-系统功能设计.md)
- [系统架构](../docs_organized/system/02-系统架构.md)
- [详细设计](../docs_organized/system/04-详细设计.md)

### 使用手册
- [使用手册](../docs_organized/guides/01-使用手册.md)
- [功能实现示例](../docs_organized/guides/02-功能实现示例.md)

## 版本历史

### v1.0.0 (2024-03-15)
- ✅ 完成基础楼宇管理功能
- ✅ 实现摄像头管理系统
- ✅ 集成视频流媒体服务
- ✅ 完善用户认证和权限管理
- ✅ 添加系统监控和备份功能
- ✅ 完成API接口文档

### 计划功能 (v1.1.0)
- 🔄 WebSocket实时通信
- 🔄 移动端适配
- 🔄 数据分析仪表板
- 🔄 报表导出功能
- 🔄 多语言支持

## 联系信息

**开发团队**：EduFusionCenter Team  
**项目维护**：持续更新中  
**技术支持**：请查看相关文档或提交Issue

---

**最后更新时间**：2024-03-15  
**文档版本**：v1.0
