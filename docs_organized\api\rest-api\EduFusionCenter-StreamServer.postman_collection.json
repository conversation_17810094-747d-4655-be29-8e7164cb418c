{"info": {"name": "EduFusionCenter - Stream Server API", "description": "Node.js流媒体服务器API测试集合 - 端口3001", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.0.0"}, "variable": [{"key": "base_url", "value": "http://localhost:3001", "type": "string"}, {"key": "test_rtsp_url", "value": "rtsp://admin:password@*************:554/stream", "type": "string"}, {"key": "test_stream_id", "value": "camera_001", "type": "string"}], "item": [{"name": "1. Health Check", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Service is healthy\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.message).to.include(\"healthy\");", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/health", "host": ["{{base_url}}"], "path": ["api", "health"]}, "description": "检查流媒体服务器和FFmpeg的运行状态"}}, {"name": "2. Test RTSP Connection", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response format is correct\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "    pm.expect(jsonData).to.have.property('message');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"rtspUrl\": \"{{test_rtsp_url}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/rtsp/test", "host": ["{{base_url}}"], "path": ["api", "rtsp", "test"]}, "description": "测试RTSP视频源的连接可用性"}}, {"name": "3. Start Video Stream (HLS)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Stream started successfully\", function () {", "    if (pm.response.code === 200) {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData.success).to.eql(true);", "        pm.expect(jsonData.data).to.have.property('streamId');", "        pm.expect(jsonData.data).to.have.property('outputUrl');", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"rtspUrl\": \"{{test_rtsp_url}}\",\n  \"streamId\": \"{{test_stream_id}}\",\n  \"format\": \"hls\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/stream/start", "host": ["{{base_url}}"], "path": ["api", "stream", "start"]}, "description": "启动RTSP视频流到HLS格式的转码"}}, {"name": "4. Start Video Stream (FLV)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"rtspUrl\": \"{{test_rtsp_url}}\",\n  \"streamId\": \"camera_002\",\n  \"format\": \"flv\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/stream/start", "host": ["{{base_url}}"], "path": ["api", "stream", "start"]}, "description": "启动RTSP视频流到FLV格式的转码"}}, {"name": "5. Get Stream Status", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status response is valid\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "    pm.expect(jsonData.data).to.have.property('activeStreams');", "    pm.expect(jsonData.data).to.have.property('streams');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/stream/status", "host": ["{{base_url}}"], "path": ["api", "stream", "status"]}, "description": "获取当前所有活跃视频流的状态信息"}}, {"name": "6. Stop Video Stream", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"streamId\": \"{{test_stream_id}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/stream/stop", "host": ["{{base_url}}"], "path": ["api", "stream", "stop"]}, "description": "停止指定的视频流转码进程"}}, {"name": "7. Stop All Streams", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/stream/stop-all", "host": ["{{base_url}}"], "path": ["api", "stream", "stop-all"]}, "description": "一次性停止所有正在运行的视频流转码进程"}}, {"name": "8. <PERSON>", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/cache/clear", "host": ["{{base_url}}"], "path": ["api", "cache", "clear"]}, "description": "清除所有HLS视频分段缓存文件"}}, {"name": "9. Access FLV Stream", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/live/camera_002.flv", "host": ["{{base_url}}"], "path": ["live", "camera_002.flv"]}, "description": "直接访问FLV格式的实时视频流"}}, {"name": "Error Test - Invalid RTSP URL", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"rtspUrl\": \"rtsp://invalid:url@192.168.1.999:554/stream\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/rtsp/test", "host": ["{{base_url}}"], "path": ["api", "rtsp", "test"]}, "description": "测试无效RTSP URL的错误处理"}}, {"name": "Error Test - Missing Parameters", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/rtsp/test", "host": ["{{base_url}}"], "path": ["api", "rtsp", "test"]}, "description": "测试缺少必要参数的错误处理"}}]}