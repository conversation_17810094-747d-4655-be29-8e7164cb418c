# EduFusionCenter API接口文档

## 项目概述

EduFusionCenter（产教融合中心）是一个集成了楼宇管理、设备监控、摄像头管理、视频流处理、用户管理等功能的综合性管理系统。系统采用Java Spring MVC + Node.js双技术栈架构，提供完整的RESTful API接口。

### 主要功能模块
- **楼宇管理**：房间信息管理、楼层布局展示
- **设备管理**：设备监控、参数配置、维护记录
- **摄像头管理**：摄像头控制、视频流处理
- **用户管理**：用户认证、权限管理
- **系统管理**：系统设置、备份管理
- **生产线管理**：产线监控、状态管理
- **视频流服务**：RTSP转码、实时流媒体

## API基础信息

### 服务器地址
- **Java Web服务器**：`http://localhost:8080/EduFusionCenter`
- **Node.js流媒体服务器**：`http://localhost:3001`

### 认证方式
- **Session认证**：基于HTTP Session的用户认证
- **无认证**：部分公开API（如健康检查）

### 请求/响应格式
- **Content-Type**：`application/json` 或 `application/x-www-form-urlencoded`
- **字符编码**：UTF-8
- **响应格式**：统一JSON格式

### 统一响应格式
```json
{
  "success": boolean,     // 操作是否成功
  "message": string,      // 人类可读的消息
  "data": object,         // 响应数据（可选）
  "error": string         // 错误信息（失败时）
}
```

### HTTP状态码说明
- **200 OK**：请求成功
- **400 Bad Request**：客户端请求错误
- **401 Unauthorized**：未授权访问
- **404 Not Found**：资源不存在
- **500 Internal Server Error**：服务器内部错误

## 视频流媒体API (Node.js服务器 - 端口3001)

### 1. 测试RTSP连接
**POST** `/api/rtsp/test`

测试RTSP视频源的连接可用性。

**请求体：**
```json
{
  "rtspUrl": "rtsp://username:password@*************:554/stream"
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "RTSP连接测试成功",
  "data": {
    "rtspUrl": "rtsp://username:password@*************:554/stream",
    "status": "connected"
  }
}
```

### 2. 启动视频流转码
**POST** `/api/stream/start`

启动RTSP视频流到Web可播放格式的实时转码。

**请求体：**
```json
{
  "rtspUrl": "rtsp://username:password@*************:554/stream",
  "streamId": "camera_001",
  "format": "hls"  // 可选：hls 或 flv，默认hls
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "流启动成功",
  "data": {
    "streamId": "camera_001",
    "outputUrl": "/hls/camera_001/playlist.m3u8",
    "format": "hls"
  }
}
```

### 3. 停止视频流转码
**POST** `/api/stream/stop`

停止指定的视频流转码进程。

**请求体：**
```json
{
  "streamId": "camera_001"
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "流停止成功"
}
```

### 4. 获取所有流状态
**GET** `/api/stream/status`

获取当前所有活跃视频流的状态信息。

**响应示例：**
```json
{
  "success": true,
  "data": {
    "activeStreams": 2,
    "streams": [
      {
        "streamId": "camera_001",
        "rtspUrl": "rtsp://*************:554/stream",
        "format": "hls",
        "outputUrl": "/hls/camera_001/playlist.m3u8",
        "startTime": "2024-03-15T10:30:00.000Z",
        "uptime": 3600000
      }
    ]
  }
}
```

### 5. 停止所有活跃流
**POST** `/api/stream/stop-all`

一次性停止所有正在运行的视频流转码进程。

**响应示例：**
```json
{
  "success": true,
  "message": "成功停止 2 个活跃流",
  "data": {
    "stoppedCount": 2,
    "totalStreams": 2
  }
}
```

### 6. 清除视频缓存
**POST** `/api/cache/clear`

清除所有HLS视频分段缓存文件。

**响应示例：**
```json
{
  "success": true,
  "message": "成功清除 3 个缓存目录",
  "data": {
    "clearedDirectories": 3,
    "cacheBasePath": "/path/to/public/hls"
  }
}
```

### 7. 健康检查
**GET** `/api/health`

检查流媒体服务器和FFmpeg的运行状态。

**响应示例：**
```json
{
  "success": true,
  "message": "Service is healthy",
  "ffmpeg": "available"
}
```

### 8. 实时FLV流访问
**GET** `/live/{streamId}.flv`

直接访问FLV格式的实时视频流。

**路径参数：**
- `streamId`：流的唯一标识符

**响应：**
- Content-Type: `video/x-flv`
- 实时视频流数据

## Java Web API (端口8080)

### 用户管理API

#### 1. 用户登录
**POST** `/login`

用户登录认证。

**请求参数（表单）：**
- `username`：用户名
- `password`：密码
- `captcha`：验证码

#### 2. 用户登出
**POST** `/logout`

用户登出，清除会话。

### 摄像头管理API

#### 1. 摄像头控制
**POST** `/camera/control`

控制摄像头的连接、断开和PTZ操作。

**请求参数（表单）：**
- `cameraId`：摄像头ID（可选，批量操作时不需要）
- `action`：操作类型
  - `connect`：连接摄像头
  - `disconnect`：断开摄像头
  - `disconnect_all`：断开所有摄像头
  - `pan_left`：向左转动
  - `pan_right`：向右转动
  - `tilt_up`：向上转动
  - `tilt_down`：向下转动
  - `zoom_in`：放大
  - `zoom_out`：缩小

**响应示例：**
```json
{
  "success": true,
  "message": "摄像头连接成功"
}
```

### 房间活动管理API

#### 1. 搜索房间活动
**GET** `/room/searchActivities`

搜索房间活动记录。

**查询参数：**
- `roomNumber`：房间号（可选）
- `activityType`：活动类型（可选）
- `startDate`：开始日期（可选）
- `endDate`：结束日期（可选）

**响应示例：**
```json
{
  "success": true,
  "data": {}
}
```

#### 2. 获取活动详情
**GET** `/room/activity/{id}`

获取指定活动的详细信息。

**路径参数：**
- `id`：活动ID

**响应示例：**
```json
{
  "success": true,
  "data": {
    "roomNumber": "101",
    "type": "会议",
    "userName": "张三",
    "startTime": "2024-04-16 14:00",
    "endTime": "2024-04-16 16:00",
    "status": "进行中",
    "notes": "项目讨论会议"
  }
}
```

### 楼层布局API

#### 1. 获取一楼房间信息
**GET** `/layout/floor1/rooms`

获取一楼所有房间的布局信息。

**响应示例：**
```json
[
  {
    "name": "101",
    "type": "教室",
    "description": "多媒体教室",
    "x": 80,
    "y": 260,
    "width": 120,
    "height": 80
  }
]
```

### 生产线管理API

#### 1. 获取产线信息
**GET** `/production/get/{id}`

获取指定产线的详细信息。

**路径参数：**
- `id`：产线ID

**响应：**
```json
{
  "id": 1,
  "name": "生产线A",
  "status": "运行中",
  "description": "主要生产线"
}
```

#### 2. 添加产线
**POST** `/production/add`

添加新的生产线。

**请求参数（表单）：**
- `name`：产线名称
- `status`：产线状态

**响应：**
- `success`：操作成功
- `error`：操作失败

### 系统管理API

#### 1. 保存安全设置
**POST** `/system/saveSecuritySettings`

更新系统安全配置。

**请求参数（表单）：**
- `minPasswordLength`：最小密码长度（默认8）
- `requireUppercase`：是否要求大写字母（默认true）
- `requireNumbers`：是否要求数字（默认true）
- `requireSpecialChars`：是否要求特殊字符（默认false）
- `maxLoginAttempts`：最大登录尝试次数（默认5）

**响应示例：**
```json
{
  "success": true,
  "message": "安全设置已保存"
}
```

#### 2. 执行系统备份
**POST** `/system/executeBackup`

立即执行系统数据备份。

**响应示例：**
```json
{
  "success": true,
  "message": "备份执行成功"
}
```

### 调试API

#### 1. 检查Spring容器状态
**GET** `/debug/container`

检查Spring容器的状态和Bean信息。

**响应示例：**
```json
{
  "success": true,
  "message": "Spring容器状态检查完成",
  "controllerBeans": ["systemController", "productionController"],
  "totalBeans": 45,
  "systemControllerExists": true,
  "productionControllerExists": true,
  "systemServiceExists": true,
  "productionServiceExists": true
}
```

## 数据模型

### Camera（摄像头）
```json
{
  "id": "integer",
  "name": "string",
  "rtspUrl": "string",
  "roomId": "integer",
  "status": "integer",  // 0:断开, 1:连接
  "type": "string",
  "description": "string"
}
```

### Room（房间）
```json
{
  "id": "integer",
  "roomNumber": "string",
  "roomName": "string",
  "floor": "integer",
  "type": "string",
  "capacity": "integer",
  "status": "string"
}
```

### User（用户）
```json
{
  "id": "integer",
  "username": "string",
  "password": "string",
  "role": "string",  // admin, user
  "email": "string",
  "createTime": "datetime"
}
```

### ProductionLine（生产线）
```json
{
  "id": "integer",
  "name": "string",
  "status": "string",
  "description": "string",
  "createTime": "datetime"
}
```

### Device（设备）
```json
{
  "id": "integer",
  "name": "string",
  "type": "string",
  "status": "string",
  "roomId": "integer",
  "parameters": "object"
}
```

## 使用示例

### JavaScript前端调用示例

#### 1. 启动视频流
```javascript
async function startVideoStream(rtspUrl, streamId) {
  try {
    const response = await fetch('http://localhost:3001/api/stream/start', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        rtspUrl: rtspUrl,
        streamId: streamId,
        format: 'hls'
      })
    });
    
    const result = await response.json();
    if (result.success) {
      console.log('流启动成功:', result.data.outputUrl);
      // 可以使用 result.data.outputUrl 来播放视频
    } else {
      console.error('流启动失败:', result.error);
    }
  } catch (error) {
    console.error('请求失败:', error);
  }
}
```

#### 2. 控制摄像头
```javascript
async function controlCamera(cameraId, action) {
  try {
    const response = await fetch('/EduFusionCenter/camera/control', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: `cameraId=${cameraId}&action=${action}`
    });
    
    const result = await response.json();
    if (result.success) {
      console.log('控制成功:', result.message);
    } else {
      console.error('控制失败:', result.message);
    }
  } catch (error) {
    console.error('请求失败:', error);
  }
}
```

#### 3. 获取流状态
```javascript
async function getStreamStatus() {
  try {
    const response = await fetch('http://localhost:3001/api/stream/status');
    const result = await response.json();
    
    if (result.success) {
      console.log('活跃流数量:', result.data.activeStreams);
      result.data.streams.forEach(stream => {
        console.log(`流ID: ${stream.streamId}, 运行时间: ${stream.uptime}ms`);
      });
    }
  } catch (error) {
    console.error('获取状态失败:', error);
  }
}
```

### cURL命令行示例

#### 1. 测试RTSP连接
```bash
curl -X POST http://localhost:3001/api/rtsp/test \
  -H "Content-Type: application/json" \
  -d '{"rtspUrl":"rtsp://admin:password@*************:554/stream"}'
```

#### 2. 启动视频流
```bash
curl -X POST http://localhost:3001/api/stream/start \
  -H "Content-Type: application/json" \
  -d '{"rtspUrl":"rtsp://admin:password@*************:554/stream","streamId":"camera_001","format":"hls"}'
```

#### 3. 获取流状态
```bash
curl -X GET http://localhost:3001/api/stream/status
```

#### 4. 摄像头控制
```bash
curl -X POST http://localhost:8080/EduFusionCenter/camera/control \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "cameraId=1&action=connect"
```

## 错误处理

### 常见错误码和处理方式

#### 视频流相关错误
- **RTSP连接失败**：检查网络连接和认证信息
- **FFmpeg不可用**：确保FFmpeg已安装并在PATH中
- **流已存在**：使用不同的streamId或先停止现有流

#### 认证相关错误
- **会话过期**：重新登录获取新会话
- **权限不足**：联系管理员获取相应权限

#### 系统相关错误
- **数据库连接失败**：检查数据库服务状态
- **服务不可用**：检查相关服务是否正常运行

## 部署说明

### 环境要求
- **Java**：JDK 8+
- **Node.js**：14.0+
- **FFmpeg**：4.0+（用于视频转码）
- **数据库**：MySQL 5.7+

### 端口配置
- **Java Web服务器**：8080（可在web.xml中配置）
- **Node.js流媒体服务器**：3001（可通过环境变量PORT配置）

### 启动顺序
1. 启动数据库服务
2. 部署Java Web应用到Tomcat
3. 启动Node.js流媒体服务器：`node stream-server.js`

---

**文档版本**：v1.0  
**最后更新**：2024-03-15  
**维护者**：EduFusionCenter开发团队
