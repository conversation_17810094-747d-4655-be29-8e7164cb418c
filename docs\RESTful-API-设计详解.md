# EduFusionCenter RTSP转码服务器 - RESTful API设计详解

## 概述

本文档详细解释了 `stream-server.js` 中RESTful API路由的设计原理、实现细节和最佳实践。

## RESTful设计原则

### 1. 核心设计原则
- **资源导向**：URL表示资源，HTTP方法表示操作
- **统一接口**：标准化的请求/响应格式
- **无状态**：每个请求包含完整的处理信息
- **可缓存**：GET请求支持缓存优化
- **分层系统**：支持负载均衡、代理等中间层

### 2. HTTP方法使用规范
- **GET**：查询资源，幂等操作，无副作用
- **POST**：创建资源或执行操作，非幂等，有副作用
- **PUT**：更新资源，幂等操作
- **DELETE**：删除资源，幂等操作

### 3. 统一响应格式
```json
{
  "success": boolean,     // 操作是否成功
  "message": string,      // 人类可读的消息
  "data": object,         // 响应数据（可选）
  "error": string         // 错误信息（失败时）
}
```

### 4. HTTP状态码规范
- **200 OK**：请求成功
- **400 Bad Request**：客户端请求错误
- **404 Not Found**：资源不存在
- **500 Internal Server Error**：服务器内部错误

## API详细设计分析

### API 1: 测试RTSP连接
```
POST /api/rtsp/test
```

**设计决策分析：**

1. **为什么使用POST而不是GET？**
   - 需要传递敏感的RTSP URL（可能包含用户名密码）
   - POST请求体比URL参数更安全
   - 每次测试都是新的网络请求，非幂等操作

2. **参数验证的重要性：**
   ```javascript
   if (!rtspUrl) {
       return res.status(400).json({
           error: '缺少必要参数: rtspUrl'
       });
   }
   ```
   - 防御性编程，避免后续处理出错
   - 提供明确的错误信息，便于客户端调试

3. **错误处理策略：**
   ```javascript
   catch (error) {
       console.error('RTSP连接测试失败:', error);
       res.status(500).json({
           success: false,
           error: error.message,
           data: { rtspUrl: req.body.rtspUrl, status: 'failed' }
       });
   }
   ```
   - 记录详细错误到服务器日志
   - 返回安全的错误信息给客户端
   - 包含上下文信息便于问题定位

### API 2: 启动视频流转码
```
POST /api/stream/start
```

**设计决策分析：**

1. **资源创建语义：**
   - 创建新的视频流转码进程
   - 占用系统资源（CPU、内存、网络）
   - 符合POST用于创建资源的语义

2. **参数设计：**
   ```javascript
   const { rtspUrl, streamId, format } = req.body;
   ```
   - `rtspUrl`: 必需，视频源地址
   - `streamId`: 必需，唯一标识符，用于后续管理
   - `format`: 可选，支持扩展性

3. **异步处理：**
   ```javascript
   const result = await startRTSPStream(rtspUrl, streamId, format);
   ```
   - 使用async/await处理异步操作
   - 等待流启动完成再返回响应
   - 确保客户端获得准确的状态信息

### API 3: 停止视频流转码
```
POST /api/stream/stop
```

**设计决策分析：**

1. **为什么使用POST而不是DELETE？**
   - 更像是"执行停止操作"而不是"删除资源"
   - 保持API风格一致性
   - POST语义更明确表达操作意图

2. **状态码设计：**
   ```javascript
   if (stopped) {
       res.json({ success: true, message: '流停止成功' });
   } else {
       res.status(404).json({ success: false, error: '流不存在' });
   }
   ```
   - 200：成功停止
   - 404：流不存在，明确区分不同的失败原因

3. **资源清理：**
   - 停止FFmpeg进程
   - 关闭客户端连接
   - 清理临时文件
   - 从内存中移除流信息

### API 4: 获取流状态
```
GET /api/stream/status
```

**设计决策分析：**

1. **GET方法的选择：**
   - 纯查询操作，无副作用
   - 幂等操作，可重复调用
   - 支持缓存优化

2. **数据转换：**
   ```javascript
   const streams = Array.from(activeStreams.entries()).map(([id, stream]) => ({
       streamId: id,
       rtspUrl: stream.rtspUrl,
       format: stream.format,
       outputUrl: stream.outputUrl,
       startTime: stream.startTime,
       uptime: Date.now() - stream.startTime.getTime()
   }));
   ```
   - 将内部Map结构转换为客户端友好的数组
   - 计算衍生信息（如运行时长）
   - 隐藏内部实现细节

3. **响应数据结构：**
   ```javascript
   {
       success: true,
       data: {
           activeStreams: streams.length,  // 汇总信息
           streams: streams                // 详细列表
       }
   }
   ```
   - 提供汇总和详细信息
   - 满足不同使用场景的需求

## 最佳实践总结

### 1. 安全性考虑
- 敏感信息通过POST请求体传递
- 不在URL中暴露认证信息
- 错误信息不泄露内部实现细节

### 2. 可维护性设计
- 统一的响应格式
- 一致的错误处理模式
- 详细的日志记录

### 3. 扩展性支持
- 可选参数设计
- 预留扩展字段
- 模块化的功能实现

### 4. 用户体验优化
- 明确的错误信息
- 丰富的状态信息
- 快速的响应时间

## 未来改进方向

1. **认证和授权**：添加API密钥或JWT认证
2. **限流控制**：防止API滥用
3. **分页支持**：大量流时的分页查询
4. **WebSocket支持**：实时状态推送
5. **监控指标**：详细的性能和健康指标
