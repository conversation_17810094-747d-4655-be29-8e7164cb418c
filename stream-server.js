/**
 * EduFusionCenter RTSP视频流转码服务器
 *
 * 功能说明：
 * 1. 接收RTSP视频流并转码为Web可播放的格式（HLS/FLV）
 * 2. 提供RESTful API接口管理视频流
 * 3. 支持多路并发视频流处理
 * 4. 自动重连和错误恢复机制
 * 5. 实时流状态监控和管理
 *
 * 依赖：
 * - express: Web服务器框架
 * - cors: 跨域资源共享中间件
 * - child_process: 用于启动FFmpeg子进程
 * - ws: WebSocket支持（预留）
 * - path: 路径处理工具
 * - fs: 文件系统操作
 *
 * 外部依赖：
 * - FFmpeg: 视频转码工具，需要安装并添加到系统PATH
 */

// 导入必需的Node.js模块
const express = require('express');        // Web服务器框架
const cors = require('cors');              // 跨域资源共享中间件
const { spawn } = require('child_process'); // 子进程管理，用于启动FFmpeg
// const WebSocket = require('ws');        // WebSocket支持（预留功能，暂未使用）
const path = require('path');              // 路径处理工具
const fs = require('fs');                  // 文件系统操作

// 创建Express应用实例
const app = express();
// 服务器端口，优先使用环境变量，默认3001
const PORT = process.env.PORT || 3001;

// ==================== 中间件配置 ====================
app.use(cors());                    // 启用CORS，允许跨域请求
app.use(express.json());            // 解析JSON请求体
app.use(express.static('public'));  // 提供静态文件服务（HLS文件等）

// ==================== 全局变量 ====================
// 存储所有活动的视频流进程信息
// Key: streamId (字符串), Value: 流对象 {process, rtspUrl, format, outputUrl, startTime, autoRestart, clients}
const activeStreams = new Map();

// 服务器配置信息（预留用于未来扩展）
// const config = {
//     ffmpegPath: 'ffmpeg',                    // FFmpeg可执行文件路径
//     outputFormats: ['flv', 'hls', 'm3u8'],  // 支持的输出格式
//     defaultFormat: 'hls'                     // 默认输出格式
// };

// ==================== 工具函数 ====================

/**
 * 检查FFmpeg是否可用
 *
 * 通过执行 'ffmpeg -version' 命令来验证FFmpeg是否正确安装并可用
 * 这是服务器启动时的必要检查，确保视频转码功能可以正常工作
 *
 * @returns {Promise<boolean>} 如果FFmpeg可用则resolve(true)，否则reject错误信息
 */
function checkFFmpeg() {
    return new Promise((resolve, reject) => {
        // 启动FFmpeg子进程，执行版本检查命令
        const ffmpeg = spawn('ffmpeg', ['-version']);

        // 监听进程退出事件
        ffmpeg.on('close', (code) => {
            if (code === 0) {
                // 退出码为0表示成功
                resolve(true);
            } else {
                // 非0退出码表示FFmpeg不可用或有问题
                reject(new Error('FFmpeg not found'));
            }
        });

        // 监听进程错误事件（如命令不存在）
        ffmpeg.on('error', (err) => {
            reject(err);
        });

        // 设置5秒超时，防止进程挂起
        setTimeout(() => {
            ffmpeg.kill();  // 强制终止进程
            reject(new Error('FFmpeg check timeout'));
        }, 5000);
    });
}

/**
 * 测试RTSP连接是否可用
 *
 * 在正式启动视频流转码之前，先测试RTSP源是否可以正常连接
 * 这可以避免启动无效的转码进程，提高系统稳定性
 *
 * @param {string} rtspUrl - RTSP流地址，格式如: rtsp://username:password@ip:port/path
 * @returns {Promise<boolean>} 连接成功返回true，失败则reject错误信息
 */
function testRTSPConnection(rtspUrl) {
    return new Promise((resolve, reject) => {
        console.log(`测试RTSP连接: ${rtspUrl}`);

        // FFmpeg测试参数配置
        const testArgs = [
            '-i', rtspUrl,      // 输入RTSP流地址
            '-t', '5',          // 只测试5秒，避免长时间占用资源
            '-f', 'null',       // 输出格式为null，不生成实际文件
            '-'                 // 输出到标准输出（丢弃）
        ];

        // 启动FFmpeg测试进程
        const testProcess = spawn('ffmpeg', testArgs);
        let hasError = false;        // 错误标志
        let errorMessage = '';       // 错误信息

        // 监听FFmpeg的错误输出（stderr）
        testProcess.stderr.on('data', (data) => {
            const msg = data.toString();

            // 检查常见的连接错误模式
            if (msg.includes('Connection refused') ||      // 连接被拒绝
                msg.includes('Connection timed out') ||    // 连接超时
                msg.includes('Invalid data found') ||      // 无效数据
                msg.includes('Server returned 401 Unauthorized') ||  // 认证失败
                msg.includes('Server returned 404 Not Found') ||     // 资源不存在
                msg.includes('No route to host')) {        // 网络不可达
                hasError = true;
                errorMessage = msg;
            }
        });

        // 监听进程退出事件
        testProcess.on('close', (code) => {
            // code参数表示进程退出码，0表示成功，非0表示失败
            // 这里主要依据hasError标志来判断，因为RTSP测试可能在检测到流信息后正常退出
            if (hasError) {
                reject(new Error(`RTSP连接测试失败: ${errorMessage}`));
            } else {
                // 没有检测到错误，认为连接成功
                resolve(true);
            }
        });

        // 监听进程错误事件
        testProcess.on('error', (err) => {
            reject(err);
        });

        // 设置10秒超时
        setTimeout(() => {
            testProcess.kill();  // 终止测试进程
            if (!hasError) {
                // 如果没有检测到错误且超时，认为连接成功
                // 某些RTSP流可能需要较长时间建立连接
                resolve(true);
            }
        }, 10000);
    });
}

/**
 * 启动RTSP视频流转码
 *
 * 这是核心功能函数，负责将RTSP视频流转码为Web可播放的格式
 * 支持HLS和FLV两种输出格式，具有自动重连和错误恢复机制
 *
 * @param {string} rtspUrl - RTSP流地址
 * @param {string} streamId - 流的唯一标识符
 * @param {string} format - 输出格式，'hls'或'flv'，默认'hls'
 * @returns {Promise<Object>} 返回流信息对象 {streamId, outputUrl, format}
 */
function startRTSPStream(rtspUrl, streamId, format = 'hls') {
    return new Promise(async (resolve, reject) => {
        // 如果流已经存在，先停止它，避免重复启动
        if (activeStreams.has(streamId)) {
            console.log(`流 ${streamId} 已存在，先停止旧流`);
            stopStream(streamId);
        }

        // 先测试RTSP连接，确保源可用
        try {
            console.log(`开始测试RTSP连接: ${streamId}`);
            await testRTSPConnection(rtspUrl);
            console.log(`RTSP连接测试成功: ${streamId}`);
        } catch (error) {
            console.error(`RTSP连接测试失败: ${streamId}, 错误: ${error.message}`);
            reject(error);
            return;
        }

        // 根据输出格式配置FFmpeg参数
        let ffmpegArgs;    // FFmpeg命令行参数数组
        let outputUrl;     // 输出流的访问URL

        if (format === 'flv') {
            // ==================== FLV格式配置 ====================
            // FLV格式适用于实时流媒体，延迟较低，但需要Flash支持
            // 使用管道输出到HTTP流，通过stdout传输数据
            outputUrl = `/live/${streamId}.flv`;
            ffmpegArgs = [
                '-rtsp_transport', 'tcp',        // 使用TCP传输RTSP，更稳定
                '-i', rtspUrl,                   // 输入RTSP流地址
                '-c:v', 'libx264',              // 视频编码器：H.264
                '-profile:v', 'baseline',        // H.264配置文件：基线，兼容性最好
                '-level', '4.1',                // H.264级别：4.1，支持高清视频
                '-preset', 'ultrafast',         // 编码速度：超快，优先实时性
                '-tune', 'zerolatency',         // 调优：零延迟，适合实时流
                '-vf', 'scale=1280:720',        // 视频滤镜：缩放到720p分辨率
                '-r', '25',                     // 帧率：25fps
                '-b:v', '1500k',                // 视频比特率：1.5Mbps
                '-maxrate', '2000k',            // 最大比特率：2Mbps
                '-bufsize', '3000k',            // 缓冲区大小：3MB
                '-c:a', 'aac',                  // 音频编码器：AAC
                '-ar', '44100',                 // 音频采样率：44.1kHz
                '-ac', '2',                     // 音频声道数：立体声
                '-b:a', '128k',                 // 音频比特率：128kbps
                '-f', 'flv',                    // 输出格式：FLV
                '-flvflags', 'no_duration_filesize',  // FLV标志：不包含时长和文件大小
                '-fflags', '+genpts',           // 格式标志：生成PTS时间戳
                '-avoid_negative_ts', 'make_zero',    // 避免负时间戳
                'pipe:1'                        // 输出到stdout管道
            ];
        } else if (format === 'hls') {
            // ==================== HLS格式配置 ====================
            // HLS (HTTP Live Streaming) 是Apple开发的流媒体协议
            // 将视频分割成小段，生成播放列表，适合Web播放，兼容性好

            // 确保HLS输出目录存在
            const hlsDir = path.join(__dirname, 'public', 'hls', streamId);
            if (!fs.existsSync(hlsDir)) {
                fs.mkdirSync(hlsDir, { recursive: true });
                console.log(`创建HLS输出目录: ${hlsDir}`);
            }

            outputUrl = `/hls/${streamId}/playlist.m3u8`;
            ffmpegArgs = [
                '-rtsp_transport', 'tcp',        // 使用TCP传输RTSP，更稳定
                '-i', rtspUrl,                   // 输入RTSP流地址
                '-c:v', 'libx264',              // 视频编码器：H.264
                '-profile:v', 'baseline',        // H.264配置文件：基线
                '-level', '4.1',                // H.264级别：4.1，支持高分辨率
                '-preset', 'veryfast',          // 编码速度：很快，平衡质量和速度
                '-tune', 'zerolatency',         // 调优：零延迟
                '-vf', 'scale=1920×1080',       // 视频滤镜：缩放到1080p（注意：这里可能有错误，应该是1920x1080）
                '-r', '30',                     // 帧率：30fps
                '-g', '50',                     // GOP大小：50帧一个关键帧组
                '-keyint_min', '25',            // 最小关键帧间隔
                '-sc_threshold', '0',           // 场景变化检测阈值：0表示禁用
                '-b:v', '2000k',                // 视频比特率：2Mbps
                '-maxrate', '2500k',            // 最大比特率：2.5Mbps
                '-bufsize', '5000k',            // 缓冲区大小：5MB
                '-c:a', 'aac',                  // 音频编码器：AAC
                '-ar', '44100',                 // 音频采样率：44.1kHz
                '-ac', '2',                     // 音频声道数：立体声
                '-b:a', '128k',                 // 音频比特率：128kbps
                '-f', 'hls',                    // 输出格式：HLS
                '-hls_time', '1',               // 每个分段时长：1秒
                '-hls_list_size', '8',          // 播放列表保留分段数：8个
                '-hls_flags', 'delete_segments+append_list+round_durations',  // HLS标志
                '-hls_allow_cache', '0',        // 禁用缓存
                '-hls_segment_type', 'mpegts',  // 分段类型：MPEG-TS
                '-hls_segment_filename', path.join(hlsDir, 'segment_%03d.ts'),  // 分段文件名模板
                '-force_key_frames', 'expr:gte(t,n_forced*2)',  // 强制关键帧：每2秒一个
                '-reconnect', '1',              // 启用重连
                '-reconnect_at_eof', '1',       // EOF时重连
                '-reconnect_streamed', '1',     // 流式重连
                path.join(hlsDir, 'playlist.m3u8')  // 输出播放列表文件路径
            ];
        }

        console.log('启动FFmpeg进程:', ffmpegArgs.join(' '));

        const ffmpeg = spawn('ffmpeg', ffmpegArgs);
        
        ffmpeg.stdout.on('data', (data) => {
            console.log(`FFmpeg stdout: ${data}`);
        });

        ffmpeg.stderr.on('data', (data) => {
            const errorMsg = data.toString();
            console.log(`FFmpeg stderr: ${errorMsg}`);

            // 检查是否是致命错误
            if (errorMsg.includes('Connection refused') ||
                errorMsg.includes('Connection timed out') ||
                errorMsg.includes('Invalid data found') ||
                errorMsg.includes('Server returned 401 Unauthorized') ||
                errorMsg.includes('Server returned 404 Not Found') ||
                errorMsg.includes('No route to host')) {
                console.error(`FFmpeg致命错误检测到: ${errorMsg}`);
                const stream = activeStreams.get(streamId);
                if (stream) {
                    stream.autoRestart = false; // 停止自动重启
                    stream.errorCount = (stream.errorCount || 0) + 1;
                    if (stream.errorCount >= 3) {
                        console.error(`流 ${streamId} 错误次数过多，停止重试`);
                        activeStreams.delete(streamId);
                        return;
                    }
                }
            }
        });

        ffmpeg.on('close', (code) => {
            console.log(`FFmpeg进程退出，代码: ${code}`);
            const stream = activeStreams.get(streamId);
            if (stream && stream.autoRestart !== false) {
                // 增加重试计数和延迟
                stream.retryCount = (stream.retryCount || 0) + 1;
                const maxRetries = 5;
                const retryDelay = Math.min(2000 * stream.retryCount, 30000); // 最大30秒延迟

                if (stream.retryCount <= maxRetries) {
                    console.log(`自动重启流: ${streamId} (第${stream.retryCount}次重试，${retryDelay}ms后重试)`);
                    setTimeout(() => {
                        if (activeStreams.has(streamId)) {
                            console.log(`重新启动FFmpeg进程: ${streamId}`);
                            startRTSPStream(rtspUrl, streamId, format).catch(console.error);
                        }
                    }, retryDelay);
                } else {
                    console.error(`流 ${streamId} 重试次数已达上限，停止重试`);
                    activeStreams.delete(streamId);
                }
            } else {
                activeStreams.delete(streamId);
            }
        });

        ffmpeg.on('error', (err) => {
            console.error('FFmpeg错误:', err);
            const stream = activeStreams.get(streamId);
            if (stream && stream.autoRestart !== false) {
                console.log(`FFmpeg错误，准备重启: ${streamId}`);
                setTimeout(() => {
                    if (activeStreams.has(streamId)) {
                        console.log(`错误恢复，重新启动FFmpeg: ${streamId}`);
                        startRTSPStream(rtspUrl, streamId, format).catch(console.error);
                    }
                }, 5000);
            } else {
                activeStreams.delete(streamId);
                reject(err);
            }
        });

        // 存储流信息
        activeStreams.set(streamId, {
            process: ffmpeg,
            rtspUrl: rtspUrl,
            format: format,
            outputUrl: outputUrl,
            startTime: new Date(),
            autoRestart: true
        });

        // 如果是FLV格式，设置管道处理
        if (format === 'flv') {
            ffmpeg.stdout.on('data', (data) => {
                const stream = activeStreams.get(streamId);
                if (stream && stream.clients) {
                    stream.clients.forEach(client => {
                        if (!client.destroyed) {
                            client.write(data);
                        }
                    });
                }
            });

            const stream = activeStreams.get(streamId);
            if (stream) {
                stream.clients = [];
            }
        }

        // 立即返回响应，不等待FFmpeg完全启动
        // FFmpeg进程已经启动，后台会继续处理
        console.log(`流 ${streamId} 启动成功，进程正在运行`);
        resolve({
            streamId: streamId,
            outputUrl: outputUrl,
            format: format
        });

        // 在后台检查流状态
        setTimeout(() => {
            if (!ffmpeg || ffmpeg.killed) {
                console.error(`流 ${streamId} 启动后进程意外退出`);
                activeStreams.delete(streamId);
            } else {
                console.log(`流 ${streamId} 后台检查：进程运行正常`);
            }
        }, 5000);
    });
}

// 停止流
function stopStream(streamId) {
    const stream = activeStreams.get(streamId);
    if (stream) {
        stream.autoRestart = false;

        if (stream.clients) {
            stream.clients.forEach(client => {
                if (!client.destroyed) {
                    client.end();
                }
            });
        }

        stream.process.kill('SIGTERM');
        activeStreams.delete(streamId);

        // 清理HLS文件
        if (stream.format === 'hls') {
            const hlsDir = path.join(__dirname, 'public', 'hls', streamId);
            if (fs.existsSync(hlsDir)) {
                fs.rmSync(hlsDir, { recursive: true, force: true });
            }
        }

        return true;
    }
    return false;
}

// 实时FLV流路由
app.get('/live/:streamId.flv', (req, res) => {
    const streamId = req.params.streamId;
    const stream = activeStreams.get(streamId);

    if (!stream || stream.format !== 'flv') {
        return res.status(404).json({ error: '流不存在或格式不匹配' });
    }

    res.writeHead(200, {
        'Content-Type': 'video/x-flv',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Range',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Connection': 'keep-alive'
    });

    if (!stream.clients) {
        stream.clients = [];
    }
    stream.clients.push(res);

    req.on('close', () => {
        if (stream.clients) {
            const index = stream.clients.indexOf(res);
            if (index > -1) {
                stream.clients.splice(index, 1);
            }
        }
    });

    req.on('error', () => {
        if (stream.clients) {
            const index = stream.clients.indexOf(res);
            if (index > -1) {
                stream.clients.splice(index, 1);
            }
        }
    });
});

// ==================== RESTful API 路由设计详解 ====================
/*
 * RESTful API设计原则：
 * 1. 使用标准HTTP方法：GET(查询)、POST(创建/操作)、PUT(更新)、DELETE(删除)
 * 2. 资源导向的URL设计：/api/{资源类型}/{操作}
 * 3. 统一的响应格式：{ success: boolean, message: string, data?: object, error?: string }
 * 4. 适当的HTTP状态码：200(成功)、400(客户端错误)、404(未找到)、500(服务器错误)
 * 5. 输入验证和错误处理：确保API的健壮性和安全性
 */

/**
 * API 1: 测试RTSP连接
 *
 * 路由：POST /api/rtsp/test
 *
 * 设计目的：
 * - 在正式启动流转码之前，预先验证RTSP源的可用性
 * - 避免启动无效的FFmpeg进程，节省系统资源
 * - 提供快速的连接诊断功能，帮助用户排查问题
 *
 * 为什么使用POST而不是GET：
 * - 虽然是"测试"操作，但需要传递敏感的RTSP URL（可能包含用户名密码）
 * - POST请求体比URL参数更安全，不会在日志中暴露敏感信息
 * - 符合RESTful设计中"非幂等操作"的原则（每次测试都是一个新的网络请求）
 *
 * 请求体：{ rtspUrl: string }
 * //请求体示例：{ "rtspUrl": "rtsp://username:password@ip:port/path" }
 * 响应格式：{ success: boolean, message: string, data: object }
 */
app.post('/api/rtsp/test', async (req, res) => {
    try {
        // 1. 参数提取和解构赋值
        // 使用ES6解构语法从请求体中提取rtspUrl参数
        const { rtspUrl } = req.body;

        // 2. 输入验证 - 防御性编程
        // 在处理任何业务逻辑之前，先验证必要参数是否存在
        // 返回400状态码表示"Bad Request"（客户端错误）
        if (!rtspUrl) {
            return res.status(400).json({
                error: '缺少必要参数: rtspUrl'
            });
        }

        // 3. 业务逻辑执行
        // 调用专门的RTSP连接测试函数，使用await等待异步操作完成
        // 这个函数会启动一个短暂的FFmpeg进程来测试连接
        await testRTSPConnection(rtspUrl);

        // 4. 成功响应 - 统一的响应格式
        // 返回200状态码（默认），包含成功标志、消息和相关数据
        res.json({
            success: true,
            message: 'RTSP连接测试成功',
            data: {
                rtspUrl: rtspUrl,        // 回显测试的URL（用于客户端确认）
                status: 'connected'      // 连接状态标识
            }
        });
    } catch (error) {
        // 5. 错误处理 - 统一的错误响应格式
        // 记录详细错误信息到服务器日志，便于调试和监控
        console.error('RTSP连接测试失败:', error);

        // 返回500状态码表示"Internal Server Error"（服务器错误）
        // 注意：不直接暴露内部错误详情给客户端，只返回安全的错误信息
        res.status(500).json({
            success: false,
            error: error.message,    // FFmpeg或网络错误的描述信息
            data: {
                rtspUrl: req.body.rtspUrl,  // 失败的URL（用于客户端确认）
                status: 'failed'            // 连接状态标识
            }
        });
    }
});

/**
 * API 2: 启动视频流转码
 *
 * 路由：POST /api/stream/start
 *
 * 设计目的：
 * - 启动RTSP视频流到Web可播放格式的实时转码
 * - 支持多种输出格式（HLS、FLV），满足不同播放需求
 * - 管理并发流处理，支持多路视频同时转码
 *
 * 为什么使用POST：
 * - 这是一个"创建资源"的操作（创建新的视频流转码进程）
 * - 操作有副作用（启动FFmpeg子进程、占用系统资源）
 * - 非幂等操作（多次调用会创建多个进程或覆盖现有流）
 *
 * 参数设计说明：
 * - rtspUrl: 必需，RTSP视频源地址
 * - streamId: 必需，流的唯一标识符，用于后续管理和访问
 * - format: 可选，输出格式（hls/flv），默认为hls
 *
 * 请求体：{ rtspUrl: string, streamId: string, format?: string }
 * 响应格式：{ success: boolean, message: string, data: object }
 */
app.post('/api/stream/start', async (req, res) => {
    try {
        // 1. 参数提取 - 支持可选参数
        // format参数是可选的，如果未提供则在startRTSPStream函数中使用默认值
        const { rtspUrl, streamId, format } = req.body;

        // 2. 必要参数验证
        // streamId是流的唯一标识符，用于：
        // - 在activeStreams Map中存储和查找流信息
        // - 生成输出文件路径和URL
        // - 客户端后续的停止、查询操作
        if (!rtspUrl || !streamId) {
            return res.status(400).json({
                error: '缺少必要参数: rtspUrl 和 streamId'
            });
        }

        // 3. 启动流转码 - 核心业务逻辑
        // startRTSPStream是一个复杂的异步函数，包含：
        // - RTSP连接测试
        // - FFmpeg进程启动
        // - 错误处理和自动重连机制
        // - 流信息存储和管理
        const result = await startRTSPStream(rtspUrl, streamId, format);

        // 4. 成功响应
        // result包含：{ streamId, outputUrl, format }
        // outputUrl是客户端可以访问的流地址
        res.json({
            success: true,
            message: '流启动成功',
            data: result
        });
    } catch (error) {
        // 5. 错误处理
        // 可能的错误类型：
        // - RTSP连接失败（网络问题、认证失败、源不存在）
        // - FFmpeg启动失败（程序未安装、参数错误）
        // - 系统资源不足（内存、CPU、磁盘空间）
        console.error('启动流失败:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * API 3: 停止视频流转码
 *
 * 路由：POST /api/stream/stop
 *
 * 设计目的：
 * - 优雅地停止指定的视频流转码进程
 * - 释放系统资源（CPU、内存、网络连接）
 * - 清理相关文件和数据结构
 *
 * 为什么使用POST而不是DELETE：
 * - 虽然是"删除资源"的操作，但这里更像是"停止服务"
 * - POST更明确地表达了"执行停止操作"的语义
 * - 保持API风格的一致性（所有操作类API都使用POST）
 *
 * 状态码设计：
 * - 200: 成功停止流
 * - 404: 流不存在（可能已经停止或从未启动）
 * - 400: 参数错误
 * - 500: 服务器内部错误
 *
 * 请求体：{ streamId: string }
 * 响应格式：{ success: boolean, message: string }
 */
app.post('/api/stream/stop', (req, res) => {
    try {
        // 1. 参数提取
        const { streamId } = req.body;

        // 2. 参数验证
        // streamId是必需的，因为需要知道停止哪个具体的流
        if (!streamId) {
            return res.status(400).json({
                error: '缺少参数: streamId'
            });
        }

        // 3. 执行停止操作
        // stopStream函数会：
        // - 设置autoRestart为false，防止自动重启
        // - 关闭所有客户端连接
        // - 终止FFmpeg进程（发送SIGTERM信号）
        // - 从activeStreams Map中删除流信息
        // - 清理HLS临时文件
        const stopped = stopStream(streamId);

        // 4. 根据操作结果返回不同响应
        if (stopped) {
            // 成功停止流
            res.json({
                success: true,
                message: '流停止成功'
            });
        } else {
            // 流不存在 - 使用404状态码
            // 这种情况可能发生在：
            // - 流ID不存在
            // - 流已经被停止
            // - 流因错误自动退出
            res.status(404).json({
                success: false,
                error: '流不存在'
            });
        }
    } catch (error) {
        // 5. 异常处理
        // 可能的异常：
        // - 进程终止失败
        // - 文件删除权限问题
        // - 系统资源清理异常
        console.error('停止流失败:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * API 4: 获取所有流状态
 *
 * 路由：GET /api/stream/status
 *
 * 设计目的：
 * - 提供系统监控和管理界面的数据支持
 * - 让管理员了解当前系统负载和资源使用情况
 * - 支持流媒体服务的运维和故障排查
 *
 * 为什么使用GET：
 * - 这是一个纯查询操作，不修改任何服务器状态
 * - 符合RESTful设计中GET用于获取资源信息的原则
 * - 幂等操作，多次调用结果一致（除非期间有流状态变化）
 * - 可以被缓存，支持浏览器和代理服务器缓存
 *
 * 无需请求参数：
 * - 获取所有流的状态，不需要指定特定流ID
 * - 简化客户端调用，一次请求获取完整信息
 *
 * 响应数据设计：
 * - activeStreams: 活跃流数量，便于快速了解系统负载
 * - streams: 详细的流信息数组，支持管理界面展示
 *
 * 响应格式：{ success: boolean, data: { activeStreams: number, streams: array } }
 */
app.get('/api/stream/status', (_req, res) => {
    // 注意：这里_req参数使用下划线前缀表示有意未使用，原因：
    // 1. 保持Express路由处理函数的标准签名
    // 2. 未来可能需要支持查询参数（如分页、过滤等）
    // 3. 便于中间件处理（如认证、日志记录等）

    // 1. 数据转换和计算
    // 将内部的Map数据结构转换为客户端友好的数组格式
    // 同时计算每个流的运行时长等衍生信息
    const streams = Array.from(activeStreams.entries()).map(([id, stream]) => ({
        streamId: id,                                           // 流的唯一标识符
        rtspUrl: stream.rtspUrl,                               // RTSP源地址（用于显示和调试）
        format: stream.format,                                 // 输出格式（hls/flv）
        outputUrl: stream.outputUrl,                           // 客户端访问URL
        startTime: stream.startTime,                           // 流启动时间（ISO字符串）
        uptime: Date.now() - stream.startTime.getTime(),      // 运行时长（毫秒）
        // 未来可以扩展的字段：
        // status: stream.process.killed ? 'stopped' : 'running',
        // clientCount: stream.clients ? stream.clients.length : 0,
        // errorCount: stream.errorCount || 0,
        // lastError: stream.lastError || null
    }));

    // 2. 构造响应数据
    // 提供汇总信息和详细列表，满足不同的使用场景
    res.json({
        success: true,
        data: {
            activeStreams: streams.length,  // 汇总：当前活跃流数量
            streams: streams,               // 详细：每个流的完整信息
            // 未来可以扩展的汇总信息：
            // totalUptime: streams.reduce((sum, s) => sum + s.uptime, 0),
            // formatDistribution: { hls: hlsCount, flv: flvCount },
            // systemLoad: process.cpuUsage(),
            // memoryUsage: process.memoryUsage()
        }
    });
});

/**
 * API: 停止所有活跃流
 * POST /api/stream/stop-all
 *
 * 功能：一次性停止所有正在运行的视频流转码进程
 * 响应：{ success: boolean, message: string, data: object }
 */
app.post('/api/stream/stop-all', (_req, res) => {
    // _req参数未使用，因为此API不需要请求参数，停止所有流
    try {
        const streamIds = Array.from(activeStreams.keys());
        let stoppedCount = 0;

        // 遍历所有活跃流并停止
        for (const streamId of streamIds) {
            if (stopStream(streamId)) {
                stoppedCount++;
            }
        }

        res.json({
            success: true,
            message: `成功停止 ${stoppedCount} 个活跃流`,
            data: {
                stoppedCount: stoppedCount,      // 实际停止的流数量
                totalStreams: streamIds.length   // 总流数量
            }
        });
    } catch (error) {
        console.error('停止所有流失败:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * API: 清除视频缓存
 * POST /api/cache/clear
 *
 * 功能：清除所有HLS视频分段缓存文件，释放磁盘空间
 * 响应：{ success: boolean, message: string, data: object }
 */
app.post('/api/cache/clear', (_req, res) => {
    // _req参数未使用，因为此API不需要请求参数，清除所有缓存
    try {
        const hlsBaseDir = path.join(__dirname, 'public', 'hls');
        let clearedCount = 0;

        // 清除HLS缓存目录
        if (fs.existsSync(hlsBaseDir)) {
            const streamDirs = fs.readdirSync(hlsBaseDir);
            for (const streamDir of streamDirs) {
                const streamPath = path.join(hlsBaseDir, streamDir);
                if (fs.statSync(streamPath).isDirectory()) {
                    // 递归删除整个流目录
                    fs.rmSync(streamPath, { recursive: true, force: true });
                    clearedCount++;
                    console.log(`清除缓存目录: ${streamPath}`);
                }
            }
        }

        // 预留：清除其他可能的缓存文件
        // const publicDir = path.join(__dirname, 'public');
        // const tempFiles = ['*.tmp', '*.temp'];

        res.json({
            success: true,
            message: `成功清除 ${clearedCount} 个缓存目录`,
            data: {
                clearedDirectories: clearedCount,
                cacheBasePath: hlsBaseDir
            }
        });
    } catch (error) {
        console.error('清除缓存失败:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

/**
 * API: 健康检查
 * GET /api/health
 *
 * 功能：检查服务器和FFmpeg的运行状态
 * 响应：{ success: boolean, message: string, ffmpeg?: string }
 */
app.get('/api/health', async (_req, res) => {
    // _req参数未使用，因为健康检查不需要请求参数
    try {
        // 检查FFmpeg是否可用
        await checkFFmpeg();
        res.json({
            success: true,
            message: 'Service is healthy',
            ffmpeg: 'available'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Service unhealthy',
            error: error.message
        });
    }
});

// 启动服务器
app.listen(PORT, '0.0.0.0', async () => {
    console.log(`EduFusionCenter RTSP转码服务器运行在端口 ${PORT}`);
    console.log(`服务器地址: http://localhost:${PORT}`);
    console.log(`服务器地址: http://127.0.0.1:${PORT}`);

    try {
        await checkFFmpeg();
        console.log('✓ FFmpeg 可用');
    } catch (error) {
        console.error('✗ FFmpeg 不可用:', error.message);
        console.log('请确保已安装FFmpeg并添加到系统PATH中');
    }
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('正在关闭服务器...');
    
    for (const [streamId] of activeStreams) {
        stopStream(streamId);
    }
    
    process.exit(0);
});
