{"info": {"name": "EduFusionCenter - Java Web API", "description": "Java Web服务器API测试集合 - 端口8080", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.0.0"}, "variable": [{"key": "base_url", "value": "http://localhost:8080/EduFusionCenter", "type": "string"}, {"key": "test_username", "value": "admin", "type": "string"}, {"key": "test_password", "value": "admin123", "type": "string"}, {"key": "test_camera_id", "value": "1", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "User Login", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Login response received\", function () {", "    pm.response.to.have.status(200);", "});", "", "// 保存session cookie", "if (pm.cookies.has('JSESSIONID')) {", "    pm.environment.set('session_cookie', pm.cookies.get('JSESSIONID'));", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "username", "value": "{{test_username}}"}, {"key": "password", "value": "{{test_password}}"}, {"key": "<PERSON><PERSON>a", "value": "1234"}]}, "url": {"raw": "{{base_url}}/login", "host": ["{{base_url}}"], "path": ["login"]}, "description": "用户登录认证"}}, {"name": "User <PERSON>", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/logout", "host": ["{{base_url}}"], "path": ["logout"]}, "description": "用户登出，清除会话"}}]}, {"name": "Camera Management", "item": [{"name": "Connect Camera", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Camera control response\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "    pm.expect(jsonData).to.have.property('message');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "cameraId", "value": "{{test_camera_id}}"}, {"key": "action", "value": "connect"}]}, "url": {"raw": "{{base_url}}/camera/control", "host": ["{{base_url}}"], "path": ["camera", "control"]}, "description": "连接指定摄像头"}}, {"name": "Disconnect Camera", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "cameraId", "value": "{{test_camera_id}}"}, {"key": "action", "value": "disconnect"}]}, "url": {"raw": "{{base_url}}/camera/control", "host": ["{{base_url}}"], "path": ["camera", "control"]}, "description": "断开指定摄像头"}}, {"name": "Disconnect All Cameras", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "action", "value": "disconnect_all"}]}, "url": {"raw": "{{base_url}}/camera/control", "host": ["{{base_url}}"], "path": ["camera", "control"]}, "description": "断开所有摄像头连接"}}, {"name": "Pan Left", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "cameraId", "value": "{{test_camera_id}}"}, {"key": "action", "value": "pan_left"}]}, "url": {"raw": "{{base_url}}/camera/control", "host": ["{{base_url}}"], "path": ["camera", "control"]}, "description": "控制摄像头向左转动"}}, {"name": "Pan Right", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "cameraId", "value": "{{test_camera_id}}"}, {"key": "action", "value": "pan_right"}]}, "url": {"raw": "{{base_url}}/camera/control", "host": ["{{base_url}}"], "path": ["camera", "control"]}, "description": "控制摄像头向右转动"}}, {"name": "Tilt Up", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "cameraId", "value": "{{test_camera_id}}"}, {"key": "action", "value": "tilt_up"}]}, "url": {"raw": "{{base_url}}/camera/control", "host": ["{{base_url}}"], "path": ["camera", "control"]}, "description": "控制摄像头向上转动"}}, {"name": "Tilt Down", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "cameraId", "value": "{{test_camera_id}}"}, {"key": "action", "value": "tilt_down"}]}, "url": {"raw": "{{base_url}}/camera/control", "host": ["{{base_url}}"], "path": ["camera", "control"]}, "description": "控制摄像头向下转动"}}, {"name": "Zoom In", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "cameraId", "value": "{{test_camera_id}}"}, {"key": "action", "value": "zoom_in"}]}, "url": {"raw": "{{base_url}}/camera/control", "host": ["{{base_url}}"], "path": ["camera", "control"]}, "description": "控制摄像头放大"}}, {"name": "Zoom Out", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "cameraId", "value": "{{test_camera_id}}"}, {"key": "action", "value": "zoom_out"}]}, "url": {"raw": "{{base_url}}/camera/control", "host": ["{{base_url}}"], "path": ["camera", "control"]}, "description": "控制摄像头缩小"}}]}, {"name": "Room Management", "item": [{"name": "Search Room Activities", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/room/searchActivities?roomNumber=101&activityType=会议&startDate=2024-03-01&endDate=2024-03-31", "host": ["{{base_url}}"], "path": ["room", "searchActivities"], "query": [{"key": "roomNumber", "value": "101"}, {"key": "activityType", "value": "会议"}, {"key": "startDate", "value": "2024-03-01"}, {"key": "endDate", "value": "2024-03-31"}]}, "description": "搜索房间活动记录"}}, {"name": "Get Activity Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/room/activity/1", "host": ["{{base_url}}"], "path": ["room", "activity", "1"]}, "description": "获取指定活动的详细信息"}}]}, {"name": "Floor Layout", "item": [{"name": "Get Floor1 Rooms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Room data is array\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/layout/floor1/rooms", "host": ["{{base_url}}"], "path": ["layout", "floor1", "rooms"]}, "description": "获取一楼所有房间的布局信息"}}]}, {"name": "Production Management", "item": [{"name": "Get Production Line", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/production/get/1", "host": ["{{base_url}}"], "path": ["production", "get", "1"]}, "description": "获取指定产线的详细信息"}}, {"name": "Add Production Line", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "name", "value": "测试产线A"}, {"key": "status", "value": "运行中"}]}, "url": {"raw": "{{base_url}}/production/add", "host": ["{{base_url}}"], "path": ["production", "add"]}, "description": "添加新的生产线"}}]}, {"name": "System Management", "item": [{"name": "Save Security Settings", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "min<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "8"}, {"key": "requireUppercase", "value": "true"}, {"key": "requireNumbers", "value": "true"}, {"key": "requireSpecialChars", "value": "false"}, {"key": "maxLogin<PERSON><PERSON><PERSON>s", "value": "5"}]}, "url": {"raw": "{{base_url}}/system/saveSecuritySettings", "host": ["{{base_url}}"], "path": ["system", "saveSecuritySettings"]}, "description": "更新系统安全配置"}}, {"name": "Execute Backup", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/system/executeBackup", "host": ["{{base_url}}"], "path": ["system", "executeBackup"]}, "description": "立即执行系统数据备份"}}]}, {"name": "Debug", "item": [{"name": "Check Container Status", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Container status check\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "    pm.expect(jsonData).to.have.property('message');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/debug/container", "host": ["{{base_url}}"], "path": ["debug", "container"]}, "description": "检查Spring容器的状态和Bean信息"}}]}]}