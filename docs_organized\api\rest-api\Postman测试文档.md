# EduFusionCenter Postman API测试文档

## 概述

本文档提供了EduFusionCenter项目的完整Postman测试配置，包含两个主要的API Collection：
1. **Node.js流媒体服务器** (端口3001) - 视频流处理API
2. **Java Web服务器** (端口8080) - 业务管理API

## 环境变量配置

### 1. 创建环境变量

在Postman中创建一个新环境，命名为"EduFusionCenter"，并添加以下变量：

```json
{
  "name": "EduFusionCenter",
  "values": [
    {
      "key": "node_base_url",
      "value": "http://localhost:3001",
      "enabled": true
    },
    {
      "key": "java_base_url", 
      "value": "http://localhost:8080/EduFusionCenter",
      "enabled": true
    },
    {
      "key": "test_rtsp_url",
      "value": "rtsp://admin:password@*************:554/stream",
      "enabled": true
    },
    {
      "key": "test_stream_id",
      "value": "camera_001",
      "enabled": true
    },
    {
      "key": "test_camera_id",
      "value": "1",
      "enabled": true
    },
    {
      "key": "test_username",
      "value": "admin",
      "enabled": true
    },
    {
      "key": "test_password",
      "value": "admin123",
      "enabled": true
    },
    {
      "key": "session_cookie",
      "value": "",
      "enabled": true
    }
  ]
}
```

## Collection 1: Node.js流媒体服务器API

### 基础信息
- **名称**: EduFusionCenter - Stream Server API
- **基础URL**: {{node_base_url}}
- **端口**: 3001

### API端点列表

#### 1. 健康检查
```json
{
  "name": "Health Check",
  "request": {
    "method": "GET",
    "header": [],
    "url": {
      "raw": "{{node_base_url}}/api/health",
      "host": ["{{node_base_url}}"],
      "path": ["api", "health"]
    }
  },
  "response": []
}
```

**预期响应**:
```json
{
  "success": true,
  "message": "Service is healthy",
  "ffmpeg": "available"
}
```

#### 2. 测试RTSP连接
```json
{
  "name": "Test RTSP Connection",
  "request": {
    "method": "POST",
    "header": [
      {
        "key": "Content-Type",
        "value": "application/json"
      }
    ],
    "body": {
      "mode": "raw",
      "raw": "{\n  \"rtspUrl\": \"{{test_rtsp_url}}\"\n}"
    },
    "url": {
      "raw": "{{node_base_url}}/api/rtsp/test",
      "host": ["{{node_base_url}}"],
      "path": ["api", "rtsp", "test"]
    }
  },
  "response": []
}
```

**测试数据变体**:
- 有效RTSP URL: `rtsp://admin:password@*************:554/stream`
- 无效RTSP URL: `rtsp://invalid:url@192.168.1.999:554/stream`
- 缺少参数: `{}`

#### 3. 启动视频流
```json
{
  "name": "Start Video Stream",
  "request": {
    "method": "POST",
    "header": [
      {
        "key": "Content-Type",
        "value": "application/json"
      }
    ],
    "body": {
      "mode": "raw",
      "raw": "{\n  \"rtspUrl\": \"{{test_rtsp_url}}\",\n  \"streamId\": \"{{test_stream_id}}\",\n  \"format\": \"hls\"\n}"
    },
    "url": {
      "raw": "{{node_base_url}}/api/stream/start",
      "host": ["{{node_base_url}}"],
      "path": ["api", "stream", "start"]
    }
  },
  "response": []
}
```

**测试场景**:
1. **HLS格式**: `"format": "hls"`
2. **FLV格式**: `"format": "flv"`
3. **默认格式**: 不包含format字段

#### 4. 获取流状态
```json
{
  "name": "Get Stream Status",
  "request": {
    "method": "GET",
    "header": [],
    "url": {
      "raw": "{{node_base_url}}/api/stream/status",
      "host": ["{{node_base_url}}"],
      "path": ["api", "stream", "status"]
    }
  },
  "response": []
}
```

#### 5. 停止视频流
```json
{
  "name": "Stop Video Stream",
  "request": {
    "method": "POST",
    "header": [
      {
        "key": "Content-Type",
        "value": "application/json"
      }
    ],
    "body": {
      "mode": "raw",
      "raw": "{\n  \"streamId\": \"{{test_stream_id}}\"\n}"
    },
    "url": {
      "raw": "{{node_base_url}}/api/stream/stop",
      "host": ["{{node_base_url}}"],
      "path": ["api", "stream", "stop"]
    }
  },
  "response": []
}
```

#### 6. 停止所有流
```json
{
  "name": "Stop All Streams",
  "request": {
    "method": "POST",
    "header": [],
    "url": {
      "raw": "{{node_base_url}}/api/stream/stop-all",
      "host": ["{{node_base_url}}"],
      "path": ["api", "stream", "stop-all"]
    }
  },
  "response": []
}
```

#### 7. 清除缓存
```json
{
  "name": "Clear Cache",
  "request": {
    "method": "POST",
    "header": [],
    "url": {
      "raw": "{{node_base_url}}/api/cache/clear",
      "host": ["{{node_base_url}}"],
      "path": ["api", "cache", "clear"]
    }
  },
  "response": []
}
```

#### 8. 访问FLV流
```json
{
  "name": "Access FLV Stream",
  "request": {
    "method": "GET",
    "header": [],
    "url": {
      "raw": "{{node_base_url}}/live/{{test_stream_id}}.flv",
      "host": ["{{node_base_url}}"],
      "path": ["live", "{{test_stream_id}}.flv"]
    }
  },
  "response": []
}
```

## Collection 2: Java Web服务器API

### 基础信息
- **名称**: EduFusionCenter - Java Web API
- **基础URL**: {{java_base_url}}
- **端口**: 8080

### 认证相关API

#### 1. 用户登录
```json
{
  "name": "User Login",
  "request": {
    "method": "POST",
    "header": [
      {
        "key": "Content-Type",
        "value": "application/x-www-form-urlencoded"
      }
    ],
    "body": {
      "mode": "urlencoded",
      "urlencoded": [
        {
          "key": "username",
          "value": "{{test_username}}"
        },
        {
          "key": "password",
          "value": "{{test_password}}"
        },
        {
          "key": "captcha",
          "value": "1234"
        }
      ]
    },
    "url": {
      "raw": "{{java_base_url}}/login",
      "host": ["{{java_base_url}}"],
      "path": ["login"]
    }
  },
  "response": []
}
```

#### 2. 用户登出
```json
{
  "name": "User Logout",
  "request": {
    "method": "POST",
    "header": [],
    "url": {
      "raw": "{{java_base_url}}/logout",
      "host": ["{{java_base_url}}"],
      "path": ["logout"]
    }
  },
  "response": []
}
```

### 摄像头管理API

#### 1. 摄像头控制
```json
{
  "name": "Camera Control",
  "request": {
    "method": "POST",
    "header": [
      {
        "key": "Content-Type",
        "value": "application/x-www-form-urlencoded"
      }
    ],
    "body": {
      "mode": "urlencoded",
      "urlencoded": [
        {
          "key": "cameraId",
          "value": "{{test_camera_id}}"
        },
        {
          "key": "action",
          "value": "connect"
        }
      ]
    },
    "url": {
      "raw": "{{java_base_url}}/camera/control",
      "host": ["{{java_base_url}}"],
      "path": ["camera", "control"]
    }
  },
  "response": []
}
```

**控制动作选项**:
- `connect` - 连接摄像头
- `disconnect` - 断开摄像头
- `disconnect_all` - 断开所有摄像头
- `pan_left` - 向左转动
- `pan_right` - 向右转动
- `tilt_up` - 向上转动
- `tilt_down` - 向下转动
- `zoom_in` - 放大
- `zoom_out` - 缩小

### 房间管理API

#### 1. 搜索房间活动
```json
{
  "name": "Search Room Activities",
  "request": {
    "method": "GET",
    "header": [],
    "url": {
      "raw": "{{java_base_url}}/room/searchActivities?roomNumber=101&activityType=会议&startDate=2024-03-01&endDate=2024-03-31",
      "host": ["{{java_base_url}}"],
      "path": ["room", "searchActivities"],
      "query": [
        {
          "key": "roomNumber",
          "value": "101"
        },
        {
          "key": "activityType",
          "value": "会议"
        },
        {
          "key": "startDate",
          "value": "2024-03-01"
        },
        {
          "key": "endDate",
          "value": "2024-03-31"
        }
      ]
    }
  },
  "response": []
}
```

#### 2. 获取活动详情
```json
{
  "name": "Get Activity Details",
  "request": {
    "method": "GET",
    "header": [],
    "url": {
      "raw": "{{java_base_url}}/room/activity/1",
      "host": ["{{java_base_url}}"],
      "path": ["room", "activity", "1"]
    }
  },
  "response": []
}
```

### 楼层布局API

#### 1. 获取一楼房间信息
```json
{
  "name": "Get Floor1 Rooms",
  "request": {
    "method": "GET",
    "header": [],
    "url": {
      "raw": "{{java_base_url}}/layout/floor1/rooms",
      "host": ["{{java_base_url}}"],
      "path": ["layout", "floor1", "rooms"]
    }
  },
  "response": []
}
```

### 生产线管理API

#### 1. 获取产线信息
```json
{
  "name": "Get Production Line",
  "request": {
    "method": "GET",
    "header": [],
    "url": {
      "raw": "{{java_base_url}}/production/get/1",
      "host": ["{{java_base_url}}"],
      "path": ["production", "get", "1"]
    }
  },
  "response": []
}
```

#### 2. 添加产线
```json
{
  "name": "Add Production Line",
  "request": {
    "method": "POST",
    "header": [
      {
        "key": "Content-Type",
        "value": "application/x-www-form-urlencoded"
      }
    ],
    "body": {
      "mode": "urlencoded",
      "urlencoded": [
        {
          "key": "name",
          "value": "测试产线A"
        },
        {
          "key": "status",
          "value": "运行中"
        }
      ]
    },
    "url": {
      "raw": "{{java_base_url}}/production/add",
      "host": ["{{java_base_url}}"],
      "path": ["production", "add"]
    }
  },
  "response": []
}
```

### 系统管理API

#### 1. 保存安全设置
```json
{
  "name": "Save Security Settings",
  "request": {
    "method": "POST",
    "header": [
      {
        "key": "Content-Type",
        "value": "application/x-www-form-urlencoded"
      }
    ],
    "body": {
      "mode": "urlencoded",
      "urlencoded": [
        {
          "key": "minPasswordLength",
          "value": "8"
        },
        {
          "key": "requireUppercase",
          "value": "true"
        },
        {
          "key": "requireNumbers",
          "value": "true"
        },
        {
          "key": "requireSpecialChars",
          "value": "false"
        },
        {
          "key": "maxLoginAttempts",
          "value": "5"
        }
      ]
    },
    "url": {
      "raw": "{{java_base_url}}/system/saveSecuritySettings",
      "host": ["{{java_base_url}}"],
      "path": ["system", "saveSecuritySettings"]
    }
  },
  "response": []
}
```

#### 2. 执行系统备份
```json
{
  "name": "Execute Backup",
  "request": {
    "method": "POST",
    "header": [],
    "url": {
      "raw": "{{java_base_url}}/system/executeBackup",
      "host": ["{{java_base_url}}"],
      "path": ["system", "executeBackup"]
    }
  },
  "response": []
}
```

### 调试API

#### 1. 检查Spring容器状态
```json
{
  "name": "Check Container Status",
  "request": {
    "method": "GET",
    "header": [],
    "url": {
      "raw": "{{java_base_url}}/debug/container",
      "host": ["{{java_base_url}}"],
      "path": ["debug", "container"]
    }
  },
  "response": []
}
```

## 测试场景和用例

### 1. 视频流完整测试流程

**测试顺序**:
1. 健康检查 → 确认服务可用
2. 测试RTSP连接 → 验证视频源
3. 启动视频流 → 开始转码
4. 获取流状态 → 确认流运行
5. 访问FLV流 → 测试流访问
6. 停止视频流 → 清理资源

### 2. 摄像头管理测试流程

**测试顺序**:
1. 用户登录 → 获取认证
2. 连接摄像头 → 启动摄像头
3. PTZ控制测试 → 测试各种控制命令
4. 断开摄像头 → 停止摄像头

### 3. 错误场景测试

**常见错误测试**:
- 无效RTSP URL
- 不存在的流ID
- 未认证的请求
- 无效的参数值
- 服务器不可用

## 导入和使用说明

### 1. 导入Collection

1. 打开Postman
2. 点击"Import"按钮
3. 选择"Raw text"
4. 粘贴下面提供的JSON Collection内容
5. 点击"Continue"和"Import"

### 2. 配置环境

1. 点击右上角的环境下拉菜单
2. 选择"Manage Environments"
3. 点击"Add"创建新环境
4. 输入环境名称"EduFusionCenter"
5. 添加上述环境变量
6. 保存并选择该环境

### 3. 测试执行建议

1. **先测试健康检查**：确保服务器正常运行
2. **按模块测试**：分别测试视频流和业务API
3. **注意依赖关系**：某些API需要先执行登录
4. **检查响应**：验证返回的状态码和数据格式
5. **清理资源**：测试完成后停止所有流

### 4. 常见问题解决

- **连接失败**：检查服务器是否启动，端口是否正确
- **认证失败**：确保已执行登录操作
- **RTSP测试失败**：检查FFmpeg是否安装，RTSP URL是否有效
- **流启动失败**：检查RTSP源是否可访问

## Postman Collection JSON文件

### Collection 1: Node.js流媒体服务器

将以下JSON内容保存为 `EduFusionCenter-StreamServer.postman_collection.json` 文件：

```json
{
  "info": {
    "name": "EduFusionCenter - Stream Server API",
    "description": "Node.js流媒体服务器API测试集合",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "variable": [
    {
      "key": "base_url",
      "value": "http://localhost:3001"
    }
  ],
  "item": [
    {
      "name": "Health Check",
      "request": {
        "method": "GET",
        "header": [],
        "url": {
          "raw": "{{base_url}}/api/health",
          "host": ["{{base_url}}"],
          "path": ["api", "health"]
        }
      }
    },
    {
      "name": "Test RTSP Connection",
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Content-Type",
            "value": "application/json"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{\n  \"rtspUrl\": \"rtsp://admin:password@*************:554/stream\"\n}"
        },
        "url": {
          "raw": "{{base_url}}/api/rtsp/test",
          "host": ["{{base_url}}"],
          "path": ["api", "rtsp", "test"]
        }
      }
    },
    {
      "name": "Start Video Stream (HLS)",
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Content-Type",
            "value": "application/json"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{\n  \"rtspUrl\": \"rtsp://admin:password@*************:554/stream\",\n  \"streamId\": \"camera_001\",\n  \"format\": \"hls\"\n}"
        },
        "url": {
          "raw": "{{base_url}}/api/stream/start",
          "host": ["{{base_url}}"],
          "path": ["api", "stream", "start"]
        }
      }
    },
    {
      "name": "Start Video Stream (FLV)",
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Content-Type",
            "value": "application/json"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{\n  \"rtspUrl\": \"rtsp://admin:password@*************:554/stream\",\n  \"streamId\": \"camera_002\",\n  \"format\": \"flv\"\n}"
        },
        "url": {
          "raw": "{{base_url}}/api/stream/start",
          "host": ["{{base_url}}"],
          "path": ["api", "stream", "start"]
        }
      }
    },
    {
      "name": "Get Stream Status",
      "request": {
        "method": "GET",
        "header": [],
        "url": {
          "raw": "{{base_url}}/api/stream/status",
          "host": ["{{base_url}}"],
          "path": ["api", "stream", "status"]
        }
      }
    },
    {
      "name": "Stop Video Stream",
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Content-Type",
            "value": "application/json"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{\n  \"streamId\": \"camera_001\"\n}"
        },
        "url": {
          "raw": "{{base_url}}/api/stream/stop",
          "host": ["{{base_url}}"],
          "path": ["api", "stream", "stop"]
        }
      }
    },
    {
      "name": "Stop All Streams",
      "request": {
        "method": "POST",
        "header": [],
        "url": {
          "raw": "{{base_url}}/api/stream/stop-all",
          "host": ["{{base_url}}"],
          "path": ["api", "stream", "stop-all"]
        }
      }
    },
    {
      "name": "Clear Cache",
      "request": {
        "method": "POST",
        "header": [],
        "url": {
          "raw": "{{base_url}}/api/cache/clear",
          "host": ["{{base_url}}"],
          "path": ["api", "cache", "clear"]
        }
      }
    },
    {
      "name": "Access FLV Stream",
      "request": {
        "method": "GET",
        "header": [],
        "url": {
          "raw": "{{base_url}}/live/camera_002.flv",
          "host": ["{{base_url}}"],
          "path": ["live", "camera_002.flv"]
        }
      }
    }
  ]
}
```

### Collection 2: Java Web服务器

将以下JSON内容保存为 `EduFusionCenter-JavaWeb.postman_collection.json` 文件：

```json
{
  "info": {
    "name": "EduFusionCenter - Java Web API",
    "description": "Java Web服务器API测试集合",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "variable": [
    {
      "key": "base_url",
      "value": "http://localhost:8080/EduFusionCenter"
    }
  ],
  "item": [
    {
      "name": "Authentication",
      "item": [
        {
          "name": "User Login",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/x-www-form-urlencoded"
              }
            ],
            "body": {
              "mode": "urlencoded",
              "urlencoded": [
                {
                  "key": "username",
                  "value": "admin"
                },
                {
                  "key": "password",
                  "value": "admin123"
                },
                {
                  "key": "captcha",
                  "value": "1234"
                }
              ]
            },
            "url": {
              "raw": "{{base_url}}/login",
              "host": ["{{base_url}}"],
              "path": ["login"]
            }
          }
        },
        {
          "name": "User Logout",
          "request": {
            "method": "POST",
            "header": [],
            "url": {
              "raw": "{{base_url}}/logout",
              "host": ["{{base_url}}"],
              "path": ["logout"]
            }
          }
        }
      ]
    },
    {
      "name": "Camera Management",
      "item": [
        {
          "name": "Connect Camera",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/x-www-form-urlencoded"
              }
            ],
            "body": {
              "mode": "urlencoded",
              "urlencoded": [
                {
                  "key": "cameraId",
                  "value": "1"
                },
                {
                  "key": "action",
                  "value": "connect"
                }
              ]
            },
            "url": {
              "raw": "{{base_url}}/camera/control",
              "host": ["{{base_url}}"],
              "path": ["camera", "control"]
            }
          }
        },
        {
          "name": "Disconnect Camera",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/x-www-form-urlencoded"
              }
            ],
            "body": {
              "mode": "urlencoded",
              "urlencoded": [
                {
                  "key": "cameraId",
                  "value": "1"
                },
                {
                  "key": "action",
                  "value": "disconnect"
                }
              ]
            },
            "url": {
              "raw": "{{base_url}}/camera/control",
              "host": ["{{base_url}}"],
              "path": ["camera", "control"]
            }
          }
        },
        {
          "name": "Disconnect All Cameras",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/x-www-form-urlencoded"
              }
            ],
            "body": {
              "mode": "urlencoded",
              "urlencoded": [
                {
                  "key": "action",
                  "value": "disconnect_all"
                }
              ]
            },
            "url": {
              "raw": "{{base_url}}/camera/control",
              "host": ["{{base_url}}"],
              "path": ["camera", "control"]
            }
          }
        },
        {
          "name": "Pan Left",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/x-www-form-urlencoded"
              }
            ],
            "body": {
              "mode": "urlencoded",
              "urlencoded": [
                {
                  "key": "cameraId",
                  "value": "1"
                },
                {
                  "key": "action",
                  "value": "pan_left"
                }
              ]
            },
            "url": {
              "raw": "{{base_url}}/camera/control",
              "host": ["{{base_url}}"],
              "path": ["camera", "control"]
            }
          }
        },
        {
          "name": "Zoom In",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/x-www-form-urlencoded"
              }
            ],
            "body": {
              "mode": "urlencoded",
              "urlencoded": [
                {
                  "key": "cameraId",
                  "value": "1"
                },
                {
                  "key": "action",
                  "value": "zoom_in"
                }
              ]
            },
            "url": {
              "raw": "{{base_url}}/camera/control",
              "host": ["{{base_url}}"],
              "path": ["camera", "control"]
            }
          }
        }
      ]
    }
  ]
}
```

---

**注意**: 请根据实际部署环境调整服务器地址和端口号。测试前确保所有相关服务（MySQL、Tomcat、Node.js）都已正常启动。
